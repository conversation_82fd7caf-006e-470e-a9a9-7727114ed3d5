import { useSelector } from "react-redux";
import { View, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, SPACING } from "b-ui-lib";
import ContactsList from "./components/ContactsList";
import { getPeopleByCompany } from "./helpers/contactFilters";

type Props = {
  companyGuid?: string;
  onPersonPress?: (person: any) => void;
};

const PeopleListScreen = ({ companyGuid, onPersonPress }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );

  // Get people for the company
  const peopleIds = companyGuid 
    ? getPeopleByCompany(contacts, companyGuid)
    : [];
  
  const people = peopleIds.map((id: string) => contacts.byId[id]).filter(Boolean);

  const handlePersonPress = (person: any) => {
    onPersonPress?.(person);
  };

  return (
    <View style={styles.container}>
      <ContactsList
        contacts={people}
        title="People"
        onContactPress={handlePersonPress}
        emptyMessage="No people found for this company"
        showContactIcons={false}
      />
    </View>
  );
};

export default PeopleListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles };
};
