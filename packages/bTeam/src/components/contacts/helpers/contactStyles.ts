import { StyleSheet } from "react-native";
import { Theme, SPACING } from "b-ui-lib";

export const createContactStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      padding: SPACING.M,
    },
    avatar: {
      width: 65,
      height: 65,
      borderRadius: 50,
    },
    headerText: {
      fontWeight: "700",
      marginBottom: SPACING.M,
    },
    contactsRow: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    contactsInfo: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.XS,
    },
  });

  return { styles, color };
};
