export const filterContactsByType = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string,
  validationType: string
) => {
  const contactContacting = contacting.allIds.filter(
    (id: string) => contacting.byId[id]?.CTG_CNT_Guid === contactGuid
  );
  
  return contactContacting.filter(
    (id: string) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        ?.CTT_ValueValidationType === validationType
  );
};

export const filterContactsByCode = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string,
  code: string
) => {
  const contactContacting = contacting.allIds.filter(
    (id: string) => contacting.byId[id]?.CTG_CNT_Guid === contactGuid
  );
  
  return contactContacting.filter(
    (id: string) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]?.CTT_Code === code
  );
};

export const getPhoneNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "phone");
};

export const getEmails = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "email");
};

export const getFaxNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "fax");
};

export const getTelexNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByCode(contacting, contactingTypes, contactGuid, "telex");
};

export const getRelatedContacts = (
  contacts: any,
  parentContactGuid: string
) => {
  return contacts.allIds.filter(
    (id: string) => contacts.byId[id]?.CNT_CNT_Guid === parentContactGuid
  );
};
