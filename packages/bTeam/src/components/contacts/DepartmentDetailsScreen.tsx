import React from "react";
import { useSelector } from "react-redux";
import { Pressable, ScrollView, StyleSheet, View } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Avatar,
  SPACING,
  IconButton,
} from "b-ui-lib";

import DetailRow from "./detailRow";

type Props = {
  onSubmit: (formData: { username: string; password: string }) => void;
  errorMessage?: string;
  clearLogInErrorMessageAction: () => void;
};

export enum ContactType {
  Company = 1, // e.g., "Test22", "Test Company 1", "ALPHA MARINE ENGINEERING", "Benefit1"
  Contact = 2, // e.g., "Admin Admin", "User User1", "User User2", etc.
  Vessel = 4, // e.g., "Test Vessel"
  Department = 8, // e.g., "Test Department", "Department2", "Benefit1Dpt"
}

const RenderContacts = (contactsList) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  return (
    <Pressable
      style={{
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: SPACING.XS,
        }}
      >
        <CustomText>{contactsList.length}</CustomText>
        <IconButton name="user" size={16} color={color.TEXT_DEFAULT} />
      </View>

      <View>
        <IconButton name="chevron-right" size={16} color={color.TEXT_DEFAULT} />
      </View>
    </Pressable>
  );
};

const resolveCompanyName = (
  message: Message,
  messages: MessagesState
): string | undefined => {
  const parent = message.CNT_CNT_Guid
    ? messages.byId[message.CNT_CNT_Guid]
    : undefined;

  if (parent?.CNT_Type === ContactType.Department && parent.CNT_CNT_Guid) {
    const company = messages.byId[parent.CNT_CNT_Guid];
    return company?.CNT_DisplayName;
  }

  if (parent?.CNT_Type === ContactType.Company) {
    return parent.CNT_DisplayName;
  }

  return undefined;
};

const resolveAddresses = (message, addresses, countries, cities) => {
  // adresses map allids and check where ADR_CNT_Guid === message.CNT_Guid
  // the address should look like this: 34, Dorikou st 151 22 Marousi Athens, Greece

  return addresses.allIds
    .map((id) => addresses.byId[id])
    .filter((address) => address.ADR_CNT_Guid === message.CNT_Guid)
    .map((address) => {
      const city = cities.byId[address.ADR_CIT_Guid];
      const country = countries.byId[address.ADR_COU_Guid];

      console.log(
        `${address.ADR_Street}, ${address.ADR_PostCode} ${city.CIT_Name}, ${country.COU_Name}`
      );

      return `${address.ADR_Street}, ${address.ADR_PostCode} ${city.CIT_Name}, ${country.COU_Name}`;
    });
};

const DepartmentDetailsScreen = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const contacting = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacting
  );
  const addresses = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.addresses
  );
  const countries = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.countries
  );
  const cities = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.cities
  );
  const contactingTypes = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contactingTypes
  );
  const contact = contacts.byId["e436d2e4-3f69-f011-be0c-6045bd2aaf50"];
  // e436d2e4-3f69-f011-be0c-6045bd2aaf50
  // e7605fec-1ddf-ef11-be00-6045bd2aaf50
  // 491a0735-c884-4902-bc9b-6d87d37b21d1

  const contactContacting = contacting.allIds.filter(
    (id) => contacting.byId[id]?.CTG_CNT_Guid === contact.CNT_Guid
  );
  const phoneNumbers = contactContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "phone"
  );
  const emails = contactContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "email"
  );
  const faxNumbers = contactContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "fax"
  );
  const contactsList = contacts.allIds.filter(
    (id) => contacts.byId[id]?.CNT_CNT_Guid === contact.CNT_Guid
  );

  return (
    <ScrollView style={styles.container}>
      <CustomText style={{ fontWeight: "700", marginBottom: SPACING.M }}>
        Department Info
      </CustomText>

      <DetailRow label="Dept Name:" value={contact.CNT_DisplayName} isBold />
      <DetailRow label="Contacts:" value={<RenderContacts contactsList />} />
      <DetailRow
        label="Adress:"
        value={resolveAddresses(
          contact,
          addresses,
          countries,
          cities
        ).toString()}
      />
      <DetailRow
        label="Company:"
        value={resolveCompanyName(contact, contacts)}
        isBold
      />
      <DetailRow
        label="Classifications"
        value={contact.Classifications.map((c) => c.CLA_Name)}
      />
      <DetailRow
        label="Phone:"
        value={phoneNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
        isPhone
      />
      <DetailRow
        label="Fax:"
        value={faxNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Tlx:" value={""} isBold />
      <DetailRow
        label="Email:"
        value={emails.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
        isMail
      />
      <DetailRow label="Notes:" value={contact.CNT_Notes} />
    </ScrollView>
  );
};

export default DepartmentDetailsScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      padding: SPACING.M,
    },
    avatar: {
      width: 65,
      height: 65,
      borderRadius: 50,
    },
  });

  return { styles, color };
};
