import React from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Avatar,
  SPACING,
} from "b-ui-lib";
import {
  addresses,
  AddressesState,
  contacting,
  contactingTypes,
  Message,
  MessagesState,
  mockMessages,
} from "./mockMessages";
import DetailRow from "./detailRow";

type Props = {
  onSubmit: (formData: { username: string; password: string }) => void;
  errorMessage?: string;
  clearLogInErrorMessageAction: () => void;
};

export enum ContactType {
  Company = 1, // e.g., "Test22", "Test Company 1", "ALPHA MARINE ENGINEERING", "Benefit1"
  Contact = 2, // e.g., "Admin Admin", "User User1", "User User2", etc.
  Vessel = 4, // e.g., "Test Vessel"
  Department = 8, // e.g., "Test Department", "Department2", "Benefit1Dpt"
}

const resolveDepartmentName = (
  message: Message,
  messages: MessagesState
): string | undefined => {
  const parent = message.CNT_CNT_Guid
    ? messages.byId[message.CNT_CNT_Guid]
    : undefined;
  return parent?.CNT_Type === ContactType.Department
    ? parent.CNT_DisplayName
    : undefined;
};

const resolveCompanyName = (
  message: Message,
  messages: MessagesState
): string | undefined => {
  const parent = message.CNT_CNT_Guid
    ? messages.byId[message.CNT_CNT_Guid]
    : undefined;

  if (parent?.CNT_Type === ContactType.Department && parent.CNT_CNT_Guid) {
    const company = messages.byId[parent.CNT_CNT_Guid];
    return company?.CNT_DisplayName;
  }

  if (parent?.CNT_Type === ContactType.Company) {
    return parent.CNT_DisplayName;
  }

  return undefined;
};

const resolveAddresses = (message: Message, addresses: AddressesState) => {
  // adresses map allids and check where ADR_CNT_Guid === message.CNT_Guid
  return addresses.allIds
    .map((id) => addresses.byId[id])
    .filter((address) => address.ADR_CNT_Guid === message.CNT_Guid);
};

const ContactDetailsScreen = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const messages: MessagesState = mockMessages;
  const message: Message =
    messages.byId["e7605fec-1ddf-ef11-be00-6045bd2aaf50"];
  // 99b47315-2fd3-47d8-b26b-6f2151fe46b4
  // e7605fec-1ddf-ef11-be00-6045bd2aaf50
  // 491a0735-c884-4902-bc9b-6d87d37b21d1

  const mockContacting = contacting.allIds.filter(
    (id) => contacting.byId[id]?.CTG_CNT_Guid === message.CNT_Guid
  );
  const phoneNumbers = mockContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "phone"
  );
  const emails = mockContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "email"
  );
  const faxNumbers = mockContacting.filter(
    (id) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        .CTT_ValueValidationType === "fax"
  );

  return (
    <ScrollView style={styles.container}>
      <CustomText style={{ fontWeight: "700", marginBottom: SPACING.M }}>
        Contact Info
      </CustomText>

      <View style={{ alignItems: "center", marginBottom: SPACING.M }}>
        <Avatar name="a" style={{ container: styles.avatar }} />
      </View>

      <DetailRow label="Name:" value={message.CNT_DisplayName} isBold />
      <DetailRow label="Title:" value={message.CNT_Prefix} />
      <DetailRow
        label="Company:"
        value={resolveCompanyName(message, messages)}
        isBold
      />
      <DetailRow
        label="Dept:"
        value={resolveDepartmentName(message, messages)}
        isBold
      />
      <DetailRow
        label="Adress:"
        value={resolveAddresses(message, addresses)
          .map((a) => a.ADR_Street)
          .join(", ")}
      />
      <DetailRow
        label="Classifications"
        value={message.Classifications.map((c) => c.CLA_Name)}
      />
      <DetailRow
        label="Phone:"
        value={phoneNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow
        label="Fax:"
        value={faxNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Tlx:" value={""} isBold />
      <DetailRow
        label="Email:"
        value={emails.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Notes:" value={message.CNT_Notes} />
    </ScrollView>
  );
};

export default ContactDetailsScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      padding: SPACING.M,
    },
    avatar: {
      width: 65,
      height: 65,
      borderRadius: 50,
    },
  });

  return { styles, color };
};
