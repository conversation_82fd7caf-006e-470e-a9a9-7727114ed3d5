import React from "react";
import { ScrollView, View } from "react-native";
import {
  useThemeAwareObject,
  CustomText,
  Avatar,
  SPACING,
} from "b-ui-lib";
import {
  addresses,
  contacting,
  contactingTypes,
  mockMessages,
} from "./mockMessages";
import DetailRow from "./detailRow";
import { ContactType } from "./helpers/contactTypes";
import {
  resolveDepartmentName,
  resolveCompanyName,
  resolveAddresses,
  type Message,
  type MessagesState
} from "./helpers/contactResolvers";
import { getPhoneNumbers, getEmails, getFaxNumbers } from "./helpers/contactFilters";
import { createContactStyles } from "./helpers/contactStyles";

type Props = {
  onSubmit: (formData: { username: string; password: string }) => void;
  errorMessage?: string;
  clearLogInErrorMessageAction: () => void;
};



const ContactDetailsScreen = ({}: Props) => {
  const { styles } = useThemeAwareObject(createContactStyles);
  const messages: MessagesState = mockMessages;
  const message: Message =
    messages.byId["e7605fec-1ddf-ef11-be00-6045bd2aaf50"];
  // 99b47315-2fd3-47d8-b26b-6f2151fe46b4
  // e7605fec-1ddf-ef11-be00-6045bd2aaf50
  // 491a0735-c884-4902-bc9b-6d87d37b21d1

  const phoneNumbers = getPhoneNumbers(contacting, contactingTypes, message.CNT_Guid);
  const emails = getEmails(contacting, contactingTypes, message.CNT_Guid);
  const faxNumbers = getFaxNumbers(contacting, contactingTypes, message.CNT_Guid);

  return (
    <ScrollView style={styles.container}>
      <CustomText style={styles.headerText}>
        Contact Info
      </CustomText>

      <View style={{ alignItems: "center", marginBottom: SPACING.M }}>
        <Avatar name="a" style={{ container: styles.avatar }} />
      </View>

      <DetailRow label="Name:" value={message.CNT_DisplayName} isBold />
      <DetailRow label="Title:" value={message.CNT_Prefix} />
      <DetailRow
        label="Company:"
        value={resolveCompanyName(message, messages)}
        isBold
      />
      <DetailRow
        label="Dept:"
        value={resolveDepartmentName(message, messages)}
        isBold
      />
      <DetailRow
        label="Adress:"
        value={resolveAddresses(message, addresses)
          .map((a) => a.ADR_Street)
          .join(", ")}
      />
      <DetailRow
        label="Classifications"
        value={message.Classifications.map((c) => c.CLA_Name)}
      />
      <DetailRow
        label="Phone:"
        value={phoneNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow
        label="Fax:"
        value={faxNumbers.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Tlx:" value={""} isBold />
      <DetailRow
        label="Email:"
        value={emails.map((id) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Notes:" value={message.CNT_Notes} />
    </ScrollView>
  );
};

export default ContactDetailsScreen;
