import React from "react";
import { StyleSheet, Text, View } from "react-native";

import { type Theme, useThemeAwareObject } from "b-ui-lib";

type Props = {};

const List = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View>
      <Text>List</Text>
    </View>
  );
};

export default List;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});

  return { styles, color };
};
