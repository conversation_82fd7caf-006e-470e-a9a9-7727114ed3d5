import { Pressable, View, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, CustomText, Avatar, SPACING, BenefitIconSet } from "b-ui-lib";
import { ContactType } from "../helpers/contactTypes";

type Props = {
  contact: any;
  onPress?: (contact: any) => void;
  showIcon?: boolean;
};

const ContactListItem = ({ contact, onPress, showIcon = true }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const getContactIcon = (contactType: number) => {
    switch (contactType) {
      case ContactType.Company:
        return "briefcase";
      case ContactType.Department:
        return "users";
      case ContactType.Vessel:
        return "cargo-ship";
      case ContactType.Contact:
        return "user";
      default:
        return "user";
    }
  };

  const getContactTypeLabel = (contactType: number) => {
    switch (contactType) {
      case ContactType.Company:
        return "Company";
      case ContactType.Department:
        return "Department";
      case ContactType.Vessel:
        return "Vessel";
      case ContactType.Contact:
        return "Person";
      default:
        return "Contact";
    }
  };

  return (
    <Pressable
      style={styles.container}
      onPress={() => onPress?.(contact)}
      android_ripple={{ color: color.RIPPLE_COLOR }}
    >
      <View style={styles.leftSection}>
        {showIcon && (
          <View style={styles.iconContainer}>
            <BenefitIconSet
              name={getContactIcon(contact.CNT_Type)}
              size={20}
              color={color.TEXT_DIMMED}
            />
          </View>
        )}
        <Avatar
          name={contact.CNT_DisplayName || "?"}
          style={{ container: styles.avatar }}
        />
      </View>

      <View style={styles.contentSection}>
        <CustomText style={styles.displayName} numberOfLines={1}>
          {contact.CNT_DisplayName || "Unknown"}
        </CustomText>
        
        {contact.CNT_Prefix && (
          <CustomText style={styles.prefix} numberOfLines={1}>
            {contact.CNT_Prefix}
          </CustomText>
        )}
        
        <CustomText style={styles.typeLabel}>
          {getContactTypeLabel(contact.CNT_Type)}
        </CustomText>
      </View>

      <View style={styles.rightSection}>
        <BenefitIconSet
          name="chevron-right"
          size={16}
          color={color.TEXT_DIMMED}
        />
      </View>
    </Pressable>
  );
};

export default ContactListItem;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      padding: SPACING.M,
      backgroundColor: color.BACKGROUND,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_EMAIL_FOLDER,
    },
    leftSection: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: SPACING.M,
    },
    iconContainer: {
      marginRight: SPACING.S,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
    },
    contentSection: {
      flex: 1,
      marginRight: SPACING.S,
    },
    displayName: {
      fontWeight: "600",
      fontSize: 16,
      color: color.TEXT_DEFAULT,
      marginBottom: SPACING.XS,
    },
    prefix: {
      fontSize: 14,
      color: color.TEXT_DIMMED,
      marginBottom: SPACING.XS,
    },
    typeLabel: {
      fontSize: 12,
      color: color.TEXT_DIMMED,
      textTransform: "uppercase",
    },
    rightSection: {
      justifyContent: "center",
    },
  });

  return { styles, color };
};
