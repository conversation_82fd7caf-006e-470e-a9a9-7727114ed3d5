import { FlatList, View, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, CustomText, SPACING } from "b-ui-lib";
import ContactListItem from "./ContactListItem";

type Props = {
  contacts: any[];
  title: string;
  onContactPress?: (contact: any) => void;
  emptyMessage?: string;
  showContactIcons?: boolean;
};

const ContactsList = ({ 
  contacts, 
  title, 
  onContactPress, 
  emptyMessage = "No contacts found",
  showContactIcons = true 
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const renderContactItem = ({ item }: { item: any }) => (
    <ContactListItem
      contact={item}
      onPress={onContactPress}
      showIcon={showContactIcons}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <CustomText style={styles.emptyText}>{emptyMessage}</CustomText>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <CustomText style={styles.headerTitle}>{title}</CustomText>
      <CustomText style={styles.headerCount}>
        {contacts.length} {contacts.length === 1 ? 'item' : 'items'}
      </CustomText>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      <FlatList
        data={contacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.CNT_Guid}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        style={styles.list}
      />
    </View>
  );
};

export default ContactsList;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    headerContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: SPACING.M,
      backgroundColor: color.BORDER_EMAIL_FOLDER,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_EMAIL_FOLDER,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: color.TEXT_DEFAULT,
    },
    headerCount: {
      fontSize: 14,
      color: color.TEXT_DIMMED,
    },
    list: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: SPACING.XL,
    },
    emptyText: {
      fontSize: 16,
      color: color.TEXT_DIMMED,
      textAlign: "center",
    },
  });

  return { styles, color };
};
