import React from "react";
import { Pressable, View } from "react-native";
import { useThemeAwareObject, CustomText, IconButton, SPACING } from "b-ui-lib";
import { createContactStyles } from "../helpers/contactStyles";

type Props = {
  contactsList: any[];
};

const RenderContacts = ({ contactsList }: Props) => {
  const { styles, color } = useThemeAwareObject(createContactStyles);
  
  return (
    <Pressable style={styles.contactsRow}>
      <View style={styles.contactsInfo}>
        <CustomText>{contactsList.length}</CustomText>
        <IconButton name="user" size={16} color={color.TEXT_DEFAULT} />
      </View>

      <View>
        <IconButton name="chevron-right" size={16} color={color.TEXT_DEFAULT} />
      </View>
    </Pressable>
  );
};

export default RenderContacts;
