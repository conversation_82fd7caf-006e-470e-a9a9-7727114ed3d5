import { useState, useCallback } from "react";
import { StyleSheet, View, Pressable, ScrollView } from "react-native";
import { type Theme, useThemeAwareObject, BenefitIconSet } from "b-ui-lib";
import CompanyDetailsScreen from "./CompanyDetailsScreen";
import VesselDetailsScreen from "./VesselDetailsScreen";
import DepartmentDetailsScreen from "./DepartmentDetailsScreen";

type Props = {};

type Tab = {
  key: string;
  testID: string;
  iconName: string;
  component: () => JSX.Element;
};

type CustomTabBarProps = {
  activeTab: number;
  onTabPress: (index: number) => void;
  tabs: Tab[];
  color: any;
};

// Custom tab bar component
function CustomTabBar({
  activeTab,
  onTabPress,
  tabs,
  color,
}: CustomTabBarProps) {
  return (
    <View
      style={{
        flexDirection: "row",
        backgroundColor: color.BORDER_EMAIL_FOLDER,
      }}
    >
      {tabs.map((tab, index) => {
        const isFocused = activeTab === index;

        return (
          <Pressable
            key={tab.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            testID={tab.testID}
            onPress={() => onTabPress(index)}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              height: 75,
              backgroundColor: isFocused
                ? color.MESSAGE_ITEM__BACKGROUND
                : color.BORDER_EMAIL_FOLDER,
              borderWidth: 1,
              borderRightColor: color.BORDER_EMAIL_FOLDER,
              borderLeftColor: color.BORDER_EMAIL_FOLDER,
              borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
              borderTopWidth: 3,
              borderTopColor: isFocused
                ? color.MESSAGE_FLAG
                : color.BORDER_EMAIL_FOLDER,
              elevation: 0,
              shadowOpacity: 0,
            }}
          >
            <BenefitIconSet
              name={tab.iconName}
              size={24}
              color={isFocused ? color.MESSAGE_FLAG : color.TEXT_DIMMED}
            />
          </Pressable>
        );
      })}
    </View>
  );
}

const GeneralContactScreen = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [activeTab, setActiveTab] = useState(0);

  // Define tabs configuration
  const tabs = [
    {
      key: "company",
      testID: "companyTab",
      iconName: "anchor",
      component: () => (
        <CompanyDetailsScreen
          onSubmit={() => {}}
          clearLogInErrorMessageAction={() => {}}
        />
      ),
    },
    {
      key: "department",
      testID: "departmentTab",
      iconName: "dashboard",
      component: () => (
        <DepartmentDetailsScreen
          onSubmit={() => {}}
          clearLogInErrorMessageAction={() => {}}
        />
      ),
    },
    {
      key: "vessel",
      testID: "vesselTab",
      iconName: "cargo-ship",
      component: () => (
        <VesselDetailsScreen
          onSubmit={() => {}}
          clearLogInErrorMessageAction={() => {}}
        />
      ),
    },
  ];

  // Handle tab press
  const handleTabPress = useCallback(
    (tabIndex: number) => {
      if (tabIndex === activeTab) return;
      setActiveTab(tabIndex);
    },
    [activeTab]
  );

  // Render tab content
  const renderTabContent = useCallback(() => {
    return tabs[activeTab].component();
  }, [activeTab, tabs]);

  return (
    <View style={styles.container}>
      <CustomTabBar
        activeTab={activeTab}
        onTabPress={handleTabPress}
        tabs={tabs}
        color={color}
      />
      <View style={{ flex: 1 }}>{renderTabContent()}</View>
    </View>
  );
};

export default GeneralContactScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
