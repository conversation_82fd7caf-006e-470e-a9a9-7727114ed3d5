import { useSelector } from "react-redux";
import { View, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, SPACING } from "b-ui-lib";
import ContactsList from "./components/ContactsList";
import { getDepartmentsByCompany } from "./helpers/contactFilters";

type Props = {
  companyGuid?: string;
  onDepartmentPress?: (department: any) => void;
};

const DepartmentsListScreen = ({ companyGuid, onDepartmentPress }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );

  // Get departments for the company
  const departmentIds = companyGuid 
    ? getDepartmentsByCompany(contacts, companyGuid)
    : [];
  
  const departments = departmentIds.map((id: string) => contacts.byId[id]).filter(Boolean);

  const handleDepartmentPress = (department: any) => {
    onDepartmentPress?.(department);
  };

  return (
    <View style={styles.container}>
      <ContactsList
        contacts={departments}
        title="Departments"
        onContactPress={handleDepartmentPress}
        emptyMessage="No departments found for this company"
        showContactIcons={false}
      />
    </View>
  );
};

export default DepartmentsListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles };
};
