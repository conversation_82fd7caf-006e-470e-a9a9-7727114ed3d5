import { useSelector } from "react-redux";
import { View, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, SPACING } from "b-ui-lib";
import ContactsList from "./components/ContactsList";
import { getVesselsByCompany } from "./helpers/contactFilters";

type Props = {
  companyGuid?: string;
  onVesselPress?: (vessel: any) => void;
};

const VesselsListScreen = ({ companyGuid, onVesselPress }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );

  // Get vessels for the company
  const vesselIds = companyGuid 
    ? getVesselsByCompany(contacts, companyGuid)
    : [];
  
  const vessels = vesselIds.map((id: string) => contacts.byId[id]).filter(Boolean);

  const handleVesselPress = (vessel: any) => {
    onVesselPress?.(vessel);
  };

  return (
    <View style={styles.container}>
      <ContactsList
        contacts={vessels}
        title="Vessels"
        onContactPress={handleVesselPress}
        emptyMessage="No vessels found for this company"
        showContactIcons={false}
      />
    </View>
  );
};

export default VesselsListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles };
};
